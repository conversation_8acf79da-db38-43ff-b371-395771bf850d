class MindmapProject {
    constructor(svgElement = null, externalData = null) {
        this.svgElement = svgElement;
        this.externalData = externalData;
        this.dragState = {
            isDragging: false,
            draggedNodeId: null,
            offset: { x: 0, y: 0 },
        };
        this.nodes = [];
        this.connections = [];
        this.workerConnections = [];
        this.eventConnections = [];

        // Zoom and Pan properties
        this.viewBox = { x: 0, y: 0, width: 1000, height: 500 };
        this.isPanning = false;
        this.panStartPoint = { x: 0, y: 0 };
        this.zoomLevel = 1;
        this.minZoom = 0.1;
        this.maxZoom = 5;

        // Minimap properties
        this.minimapContainer = null;
        this.minimapSvg = null;
        this.minimapScale = 0.15;
        this.minimapWidth = 200;
        this.minimapHeight = 150;
        this.viewportRect = null;
        this.isMinimapDragging = false;

        this.initializeMindmap();

        // If an external SVG element is provided, set up zoom and pan
        if (this.svgElement) {
            console.log('Setting up zoom and pan for external SVG element');
            this.setupZoomAndPan(this.svgElement);
            this.addZoomControlsToContainer();
            this.updateViewBox(); // Set initial viewBox
        }

        // Bind methods to preserve 'this' context
        this.handleMouseMove = this.handleMouseMove.bind(this);
        this.handleMouseUp = this.handleMouseUp.bind(this);
        this.handleMinimapMouseDown = this.handleMinimapMouseDown.bind(this);
        this.handleMinimapMouseMove = this.handleMinimapMouseMove.bind(this);
        this.handleMinimapMouseUp = this.handleMinimapMouseUp.bind(this);

        // Bind zoom and pan methods
        this.handlePanStart = this.handlePanStart.bind(this);
        this.handlePanMove = this.handlePanMove.bind(this);
        this.handlePanEnd = this.handlePanEnd.bind(this);
        this.handleWheel = this.handleWheel.bind(this);
    }

    initializeMindmap(externalData = null) {
        const dataToUse = externalData || this.externalData;
        const mindmapData = this.createMindmapData(dataToUse);
        this.nodes = mindmapData.nodes;
        this.connections = mindmapData.connections;
        this.workerConnections = mindmapData.workerConnections;
        this.eventConnections = mindmapData.eventConnections;
    }

    createMindmapData(externalData = null) {
        // If external data is provided, use it
        if (externalData) {
            return this.processExternalData(externalData);
        }

        // Fallback to hardcoded data if no external data provided
        const nodes = [
            {
                id: 'enhanced-file-reader',
                text: 'Enhanced File Reader',
                filename: 'reader.js',
                x: 75,
                y: 400,
                width: 140,
                height: 40,
                hasImport: true,
                hasExport: true,
                hasEvent: true,
                hasWorker: true,
            },
            {
                id: 'streaming-processor',
                text: 'Streaming Processor',
                filename: 'stream.js',
                x: 200,
                y: 230,
                width: 130,
                height: 40,
                hasImport: true,
                hasExport: true,
                hasEvent: true,
                hasWorker: true,
            },
            {
                id: 'size-validation',
                text: 'Size Validation',
                filename: 'validate.json',
                x: 200,
                y: 300,
                width: 110,
                height: 40,
                hasImport: true,
                hasExport: false,
                hasEvent: false,
                hasWorker: false,
            },
            {
                id: 'permission-checks',
                text: 'Permission Checks',
                filename: 'auth.html',
                x: 200,
                y: 370,
                width: 120,
                height: 40,
                hasImport: true,
                hasExport: false,
                hasEvent: false,
                hasWorker: false,
            },
            {
                id: 'timeout-protection',
                text: 'Timeout Protection',
                filename: 'timeout.js',
                x: 200,
                y: 440,
                width: 130,
                height: 40,
                hasImport: true,
                hasExport: false,
                hasEvent: true,
                hasWorker: false,
            },
            {
                id: 'resilient-worker-pool',
                text: 'Resilient Worker Pool',
                filename: 'workers.js',
                x: 370,
                y: 195,
                width: 140,
                height: 40,
                hasImport: true,
                hasExport: true,
                hasEvent: true,
                hasWorker: true,
            },
            {
                id: 'chunked-processing',
                text: 'Chunked Processing',
                filename: 'chunks.css',
                x: 370,
                y: 265,
                width: 130,
                height: 40,
                hasImport: true,
                hasExport: true,
                hasEvent: false,
                hasWorker: true,
            },
            {
                id: 'backpressure-control',
                text: 'Backpressure Control',
                filename: 'pressure.js',
                x: 370,
                y: 335,
                width: 140,
                height: 40,
                hasImport: true,
                hasExport: true,
                hasEvent: true,
                hasWorker: false,
            },
            {
                id: 'memory-safe-parser',
                text: 'Memory-Safe Parser',
                filename: 'parser.js',
                x: 550,
                y: 125,
                width: 130,
                height: 40,
                hasImport: true,
                hasExport: true,
                hasEvent: false,
                hasWorker: true,
            },
            {
                id: 'worker-health-monitor',
                text: 'Worker Health Monitor',
                filename: 'health.html',
                x: 550,
                y: 195,
                width: 140,
                height: 40,
                hasImport: true,
                hasExport: true,
                hasEvent: true,
                hasWorker: true,
            },
            {
                id: 'graceful-termination',
                text: 'Graceful Termination',
                filename: 'shutdown.json',
                x: 550,
                y: 265,
                width: 130,
                height: 40,
                hasImport: true,
                hasExport: true,
                hasEvent: true,
                hasWorker: false,
            },
            {
                id: 'error-recovery',
                text: 'Error Recovery',
                filename: 'errors.css',
                x: 550,
                y: 335,
                width: 110,
                height: 40,
                hasImport: true,
                hasExport: true,
                hasEvent: true,
                hasWorker: false,
            },
            {
                id: 'progressive-results',
                text: 'Progressive Results',
                filename: 'results.svg',
                x: 720,
                y: 20,
                width: 130,
                height: 40,
                hasImport: true,
                hasExport: false,
                hasEvent: true,
                hasWorker: false,
            },
            {
                id: 'incremental-aggregator',
                text: 'Incremental Aggregator',
                filename: 'aggregate.js',
                x: 720,
                y: 55,
                width: 150,
                height: 40,
                hasImport: true,
                hasExport: false,
                hasEvent: false,
                hasWorker: true,
            },
            {
                id: 'memory-cleanup',
                text: 'Memory Cleanup',
                filename: 'cleanup.img',
                x: 720,
                y: 90,
                width: 120,
                height: 40,
                hasImport: true,
                hasExport: false,
                hasEvent: false,
                hasWorker: true,
            },
            {
                id: 'regex-timeouts',
                text: 'Regex Timeouts',
                filename: 'regex.json',
                x: 720,
                y: 125,
                width: 110,
                height: 40,
                hasImport: true,
                hasExport: false,
                hasEvent: false,
                hasWorker: true,
            },
            {
                id: 'memory-limits',
                text: 'Memory Limits',
                filename: 'limits.css',
                x: 720,
                y: 195,
                width: 110,
                height: 40,
                hasImport: true,
                hasExport: false,
                hasEvent: true,
                hasWorker: false,
            },
            {
                id: 'corruption-detection',
                text: 'Corruption Detection',
                filename: 'detect.html',
                x: 720,
                y: 265,
                width: 140,
                height: 40,
                hasImport: true,
                hasExport: false,
                hasEvent: true,
                hasWorker: false,
            },
        ];

        // Connection definitions
        const connections = [
            { from: 'enhanced-file-reader', to: 'streaming-processor' },
            { from: 'enhanced-file-reader', to: 'size-validation' },
            { from: 'enhanced-file-reader', to: 'permission-checks' },
            { from: 'enhanced-file-reader', to: 'timeout-protection' },
            { from: 'streaming-processor', to: 'resilient-worker-pool' },
            { from: 'streaming-processor', to: 'chunked-processing' },
            { from: 'size-validation', to: 'backpressure-control' },
            { from: 'resilient-worker-pool', to: 'memory-safe-parser' },
            { from: 'resilient-worker-pool', to: 'worker-health-monitor' },
            { from: 'chunked-processing', to: 'graceful-termination' },
            { from: 'backpressure-control', to: 'error-recovery' },
            { from: 'memory-safe-parser', to: 'progressive-results' },
            { from: 'memory-safe-parser', to: 'incremental-aggregator' },
            { from: 'memory-safe-parser', to: 'memory-cleanup' },
            { from: 'memory-safe-parser', to: 'regex-timeouts' },
            { from: 'worker-health-monitor', to: 'memory-limits' },
            { from: 'graceful-termination', to: 'corruption-detection' },
        ];

        // Special worker creation connections
        const workerConnections = [
            { from: 'enhanced-file-reader', to: 'streaming-processor', type: 'worker' },
            { from: 'streaming-processor', to: 'resilient-worker-pool', type: 'worker' },
            { from: 'resilient-worker-pool', to: 'memory-safe-parser', type: 'worker' },
            { from: 'memory-safe-parser', to: 'incremental-aggregator', type: 'worker' },
        ];

        // Event flow connections
        const eventConnections = [
            { from: 'timeout-protection', to: 'backpressure-control', type: 'event' },
            { from: 'worker-health-monitor', to: 'graceful-termination', type: 'event' },
            { from: 'graceful-termination', to: 'error-recovery', type: 'event' },
        ];

        return { nodes, connections, workerConnections, eventConnections };
    }

    /**
     * Process external data and convert it to mindmap format
     * @param {Object} externalData - External data object
     * @param {Array} externalData.nodes - Array of node objects from scanner
     * @param {Array} externalData.links - Array of link objects from scanner
     * @returns {Object} - Processed mindmap data
     */
    processExternalData(externalData) {
        const { nodes: externalNodes = [], links: externalLinks = [] } = externalData;
        
        console.log('MindmapProject.processExternalData - Processing', externalNodes.length, 'nodes and', externalLinks.length, 'links');
        
        // Convert external nodes to mindmap format
        const nodes = externalNodes.map((node, index) => {
            // Calculate automatic positioning in a grid layout
            const cols = Math.ceil(Math.sqrt(externalNodes.length));
            const row = Math.floor(index / cols);
            const col = index % cols;
            const spacing = 180;
            const offsetX = 100;
            const offsetY = 100;
            
            // Extract actual data from the scanner's node structure
            const nodeData = {
                id: node.id || node.path || `node-${index}`,
                text: node.name || this.extractFilename(node.path || ''),
                filename: this.extractFilename(node.path || ''),
                x: offsetX + (col * spacing),
                y: offsetY + (row * spacing),
                width: this.calculateNodeWidth(node.name || this.extractFilename(node.path || '')),
                height: 40,
                
                // Use actual scanner data for capabilities
                hasImport: this.determineHasImport(node),
                hasExport: this.determineHasExport(node),
                hasEvent: this.determineHasEvent(node),
                hasWorker: this.determineHasWorker(node),
                
                // Store original node data for reference
                originalNode: node
            };
            
            return nodeData;
        });

        // Convert external links to connections using actual IDs/paths
        const connections = externalLinks
            .filter(link => link.source && link.target)
            .map(link => ({
                from: link.source,
                to: link.target,
                type: link.type || 'dependency'
            }));

        // Separate worker and event connections based on link types and node properties
        const workerConnections = [];
        const eventConnections = [];

        connections.forEach(conn => {
            const sourceNode = nodes.find(n => n.id === conn.from);
            const targetNode = nodes.find(n => n.id === conn.to);
            
            if (sourceNode && targetNode) {
                // Determine connection type based on link type or node capabilities
                if (conn.type === 'worker' || (sourceNode.hasWorker && targetNode.hasWorker)) {
                    workerConnections.push({ ...conn, type: 'worker' });
                } else if (conn.type === 'event' || (sourceNode.hasEvent && targetNode.hasEvent)) {
                    eventConnections.push({ ...conn, type: 'event' });
                }
            }
        });

        console.log('MindmapProject.processExternalData - Created', nodes.length, 'nodes,', connections.length, 'connections,', workerConnections.length, 'worker connections,', eventConnections.length, 'event connections');

        return { nodes, connections, workerConnections, eventConnections };
    }

    /**
     * Extract filename from path
     * @param {string} path - File path
     * @returns {string} - Filename
     */
    extractFilename(path) {
        if (!path) return 'unknown';
        return path.split('/').pop() || path.split('\\').pop() || path;
    }

    /**
     * Calculate node width based on text length
     * @param {string} text - Node text
     * @returns {number} - Width in pixels
     */
    calculateNodeWidth(text) {
        const baseWidth = 80;
        const charWidth = 8;
        return Math.max(baseWidth, Math.min(200, text.length * charWidth));
    }

    /**
     * Determine if node has import capability
     * @param {Object} node - Node data from scanner
     * @returns {boolean}
     */
    determineHasImport(node) {
        // Check actual scanner data structure
        return !!(
            (node.imports && node.imports.length > 0) ||
            (node.importsCount && node.importsCount > 0) ||
            (node.category && ['entry', 'component', 'service'].includes(node.category)) ||
            (node.type && ['js', 'jsx', 'ts', 'tsx'].includes(node.type))
        );
    }

    /**
     * Determine if node has export capability
     * @param {Object} node - Node data from scanner
     * @returns {boolean}
     */
    determineHasExport(node) {
        // Check actual scanner data structure
        return !!(
            (node.exports && node.exports.length > 0) ||
            (node.exportsCount && node.exportsCount > 0) ||
            (node.category && ['component', 'service', 'utility'].includes(node.category)) ||
            (node.type && ['js', 'jsx', 'ts', 'tsx'].includes(node.type))
        );
    }

    /**
     * Determine if node has event capability
     * @param {Object} node - Node data from scanner
     * @returns {boolean}
     */
    determineHasEvent(node) {
        // Check actual scanner data structure
        return !!(
            (node.events && node.events.length > 0) ||
            (node.eventsCount && node.eventsCount > 0) ||
            node.hasEvents ||
            (node.category && ['component', 'service'].includes(node.category)) ||
            (node.functionsCount && node.functionsCount > 0)
        );
    }

    /**
     * Determine if node has worker capability
     * @param {Object} node - Node data from scanner
     * @returns {boolean}
     */
    determineHasWorker(node) {
        // Check actual scanner data structure
        return !!(
            (node.category && ['service', 'utility'].includes(node.category)) ||
            (node.isComplex && node.complexity > 5) ||
            (node.type && ['js', 'ts'].includes(node.type)) ||
            (node.functionsCount && node.functionsCount > 3) ||
            (node.classesCount && node.classesCount > 0)
        );
    }

    /**
     * Creates the DOM elements for a mind map container.
     * @returns {DocumentFragment} A fragment containing the style and container elements, ready to be appended to the DOM.
     */
    createMindmapElement() {
        // A DocumentFragment is a lightweight container to hold nodes before appending them to the main DOM.
        const fragment = document.createDocumentFragment();

        // 1. Create the <style> element
        const style = document.createElement('style');
        style.textContent = `
            .container { max-width: 1200px; margin: 0 auto; }
            .mindmap-container { background: #1f2937; border-radius: 8px; padding: 24px; }
            svg { width: 100%; height: auto; }
        `;

        // 2. Create the main container div
        const containerDiv = document.createElement('div');
        containerDiv.className = 'container';

        // 3. Create the mindmap container div
        const mindmapContainerDiv = document.createElement('div');
        mindmapContainerDiv.className = 'mindmap-container';

        // 4. Create the SVG element
        // IMPORTANT: SVG elements must be created with a namespace
        const svgNS = "http://www.w3.org/2000/svg";
        const svg = document.createElementNS(svgNS, 'svg');
        svg.setAttribute('width', '1000');
        svg.setAttribute('height', '500');
        svg.setAttribute('viewBox', `${this.viewBox.x} ${this.viewBox.y} ${this.viewBox.width} ${this.viewBox.height}`);
        svg.setAttribute('id', 'mindmap-svg');

        // Add zoom and pan event listeners
        this.setupZoomAndPan(svg);

        // 5. Create zoom controls
        const zoomControls = this.createZoomControls();

        // 6. Assemble the elements like building blocks
        mindmapContainerDiv.appendChild(svg);
        mindmapContainerDiv.appendChild(zoomControls);
        containerDiv.appendChild(mindmapContainerDiv);

        // 6. Add the style and the main container to the fragment
        fragment.appendChild(style);
        fragment.appendChild(containerDiv);

        return fragment;
    }

    // Calculate connection paths
    getConnectionPath(from, to) {
        const fromNode = this.nodes.find((n) => n.id === from);
        const toNode = this.nodes.find((n) => n.id === to);

        const fromX = fromNode.x + fromNode.width;
        const fromY = fromNode.y + fromNode.height / 2;
        const toX = toNode.x;
        const toY = toNode.y + toNode.height / 2;

        // Create curved path
        const midX = fromX + (toX - fromX) * 0.5;
        const controlX1 = fromX + (midX - fromX) * 0.7;
        const controlX2 = toX - (toX - midX) * 0.7;

        return `M ${fromX} ${fromY} C ${controlX1} ${fromY}, ${controlX2} ${toY}, ${toX} ${toY}`;
    }

    // Calculate special connection paths for worker/event flows
    getSpecialConnectionPath(from, to, type) {
        const fromNode = this.nodes.find((n) => n.id === from);
        const toNode = this.nodes.find((n) => n.id === to);

        let fromX, fromY, toX, toY;

        if (type === 'worker') {
            // Worker connections: from top blue circle to top blue circle
            fromX = fromNode.x + fromNode.width / 2;
            fromY = fromNode.y - 8;
            toX = toNode.x + toNode.width / 2;
            toY = toNode.y - 8;
        } else if (type === 'event') {
            // Event connections: from bottom yellow circle to bottom yellow circle
            fromX = fromNode.x + fromNode.width / 2;
            fromY = fromNode.y + fromNode.height + 8;
            toX = toNode.x + toNode.width / 2;
            toY = toNode.y + toNode.height + 8;
        }

        // Create curved path
        const midX = fromX + (toX - fromX) * 0.5;
        const controlX1 = fromX + (midX - fromX) * 0.7;
        const controlX2 = toX - (toX - midX) * 0.7;

        return {
            path: `M ${fromX} ${fromY} C ${controlX1} ${fromY}, ${controlX2} ${toY}, ${toX} ${toY}`,
            endX: toX,
            endY: toY,
        };
    }

    setSvgElement(svgElement) {
        this.svgElement = svgElement;
    }

    handleMouseDown(e, nodeId) {
        e.preventDefault();
        if (!this.svgElement) return;

        const svgRect = this.svgElement.getBoundingClientRect();
        const svgPoint = {
            x: (e.clientX - svgRect.left) * (1000 / svgRect.width),
            y: (e.clientY - svgRect.top) * (500 / svgRect.height),
        };

        const node = this.nodes.find((n) => n.id === nodeId);
        this.dragState = {
            isDragging: true,
            draggedNodeId: nodeId,
            offset: {
                x: svgPoint.x - node.x,
                y: svgPoint.y - node.y,
            },
        };

        this.addEventListeners();
    }

    handleMouseMove(e) {
        if (!this.dragState.isDragging || !this.dragState.draggedNodeId || !this.svgElement) return;

        e.preventDefault();
        const svgRect = this.svgElement.getBoundingClientRect();
        const svgPoint = {
            x: (e.clientX - svgRect.left) * (1000 / svgRect.width),
            y: (e.clientY - svgRect.top) * (500 / svgRect.height),
        };

        this.nodes = this.nodes.map((node) =>
            node.id === this.dragState.draggedNodeId
                ? {
                      ...node,
                      x: Math.max(
                          0,
                          Math.min(1000 - node.width, svgPoint.x - this.dragState.offset.x),
                      ),
                      y: Math.max(
                          0,
                          Math.min(500 - node.height, svgPoint.y - this.dragState.offset.y),
                      ),
                  }
                : node,
        );

        // Trigger re-render
        this.render();
    }

    handleMouseUp() {
        this.dragState = {
            isDragging: false,
            draggedNodeId: null,
            offset: { x: 0, y: 0 },
        };

        this.removeEventListeners();
    }

    addEventListeners() {
        document.addEventListener('mousemove', this.handleMouseMove);
        document.addEventListener('mouseup', this.handleMouseUp);
    }

    removeEventListeners() {
        document.removeEventListener('mousemove', this.handleMouseMove);
        document.removeEventListener('mouseup', this.handleMouseUp);
    }

    /**
     * Setup zoom and pan functionality for the SVG element
     * @param {SVGElement} svg - The SVG element to add zoom and pan to
     */
    setupZoomAndPan(svg) {
        console.log('Setting up zoom and pan event listeners on SVG:', svg);

        // Pan event listeners
        svg.addEventListener('mousedown', this.handlePanStart);
        svg.addEventListener('wheel', this.handleWheel, { passive: false });

        // Prevent context menu on right click
        svg.addEventListener('contextmenu', (e) => e.preventDefault());

        // Set cursor style
        svg.style.cursor = 'grab';

        console.log('Zoom and pan event listeners added successfully');
    }

    /**
     * Handle pan start (mouse down on SVG background)
     * @param {MouseEvent} e - Mouse event
     */
    handlePanStart(e) {
        console.log('Pan start triggered, button:', e.button, 'target:', e.target);

        // Only pan on left mouse button and not on nodes
        if (e.button !== 0 || e.target.closest('.node')) {
            console.log('Pan start cancelled - wrong button or clicked on node');
            return;
        }

        console.log('Starting pan operation');
        e.preventDefault();
        this.isPanning = true;
        this.panStartPoint = { x: e.clientX, y: e.clientY };

        // Add pan move and end listeners
        document.addEventListener('mousemove', this.handlePanMove);
        document.addEventListener('mouseup', this.handlePanEnd);

        // Change cursor
        if (this.svgElement) {
            this.svgElement.style.cursor = 'grabbing';
        }
    }

    /**
     * Handle pan move (mouse move while panning)
     * @param {MouseEvent} e - Mouse event
     */
    handlePanMove(e) {
        if (!this.isPanning || !this.svgElement) return;

        e.preventDefault();

        // Get SVG dimensions
        const svgRect = this.svgElement.getBoundingClientRect();

        // Calculate pan delta
        const deltaX = (this.panStartPoint.x - e.clientX) * (this.viewBox.width / svgRect.width);
        const deltaY = (this.panStartPoint.y - e.clientY) * (this.viewBox.height / svgRect.height);

        // Update viewBox
        this.viewBox.x += deltaX;
        this.viewBox.y += deltaY;

        // Update pan start point for next move
        this.panStartPoint = { x: e.clientX, y: e.clientY };

        // Apply the new viewBox
        this.updateViewBox();
        this.updateMinimap();
    }

    /**
     * Handle pan end (mouse up)
     * @param {MouseEvent} e - Mouse event
     */
    handlePanEnd(e) {
        this.isPanning = false;

        // Remove pan listeners
        document.removeEventListener('mousemove', this.handlePanMove);
        document.removeEventListener('mouseup', this.handlePanEnd);

        // Reset cursor
        if (this.svgElement) {
            this.svgElement.style.cursor = 'grab';
        }
    }

    /**
     * Handle mouse wheel for zooming
     * @param {WheelEvent} e - Wheel event
     */
    handleWheel(e) {
        e.preventDefault();

        const zoomIntensity = 0.1;
        const zoomFactor = e.deltaY < 0 ? (1 - zoomIntensity) : (1 + zoomIntensity);

        // Get the SVG element from the event target
        const svgElement = e.currentTarget;
        if (!svgElement) {
            console.error('SVG element not found in wheel event');
            return;
        }

        // Get mouse position relative to SVG
        const svgRect = svgElement.getBoundingClientRect();
        const mouseX = e.clientX - svgRect.left;
        const mouseY = e.clientY - svgRect.top;

        // Convert mouse position to SVG coordinates
        const svgMouseX = this.viewBox.x + (mouseX / svgRect.width) * this.viewBox.width;
        const svgMouseY = this.viewBox.y + (mouseY / svgRect.height) * this.viewBox.height;

        // Calculate new viewBox dimensions
        const newWidth = this.viewBox.width * zoomFactor;
        const newHeight = this.viewBox.height * zoomFactor;

        // Check zoom limits
        const newZoomLevel = 1000 / newWidth; // Base width is 1000
        if (newZoomLevel < this.minZoom || newZoomLevel > this.maxZoom) {
            return; // Don't zoom if it would exceed limits
        }

        // Calculate new viewBox position to zoom towards mouse
        const newX = svgMouseX - (mouseX / svgRect.width) * newWidth;
        const newY = svgMouseY - (mouseY / svgRect.height) * newHeight;

        // Update viewBox
        this.viewBox.x = newX;
        this.viewBox.y = newY;
        this.viewBox.width = newWidth;
        this.viewBox.height = newHeight;
        this.zoomLevel = newZoomLevel;

        // Apply the new viewBox
        this.updateViewBox();
        this.updateMinimap();
    }

    /**
     * Update the SVG viewBox attribute
     */
    updateViewBox() {
        if (this.svgElement) {
            const viewBoxString = `${this.viewBox.x} ${this.viewBox.y} ${this.viewBox.width} ${this.viewBox.height}`;
            this.svgElement.setAttribute('viewBox', viewBoxString);
            console.log('Updated viewBox:', viewBoxString);
        } else {
            console.warn('SVG element not available for viewBox update');
        }
    }

    /**
     * Reset zoom and pan to default view
     */
    resetView() {
        this.viewBox = { x: 0, y: 0, width: 1000, height: 500 };
        this.zoomLevel = 1;
        this.updateViewBox();
        this.updateMinimap();
    }

    /**
     * Zoom to fit all nodes
     */
    zoomToFit() {
        if (this.nodes.length === 0) {
            this.resetView();
            return;
        }

        // Calculate bounding box of all nodes
        let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;

        this.nodes.forEach(node => {
            minX = Math.min(minX, node.x);
            minY = Math.min(minY, node.y);
            maxX = Math.max(maxX, node.x + node.width);
            maxY = Math.max(maxY, node.y + node.height);
        });

        // Add padding
        const padding = 50;
        minX -= padding;
        minY -= padding;
        maxX += padding;
        maxY += padding;

        // Calculate new viewBox
        const width = maxX - minX;
        const height = maxY - minY;

        this.viewBox = { x: minX, y: minY, width, height };
        this.zoomLevel = 1000 / width; // Base width is 1000

        this.updateViewBox();
        this.updateMinimap();
    }

    /**
     * Add zoom controls to the container of an external SVG element
     */
    addZoomControlsToContainer() {
        if (!this.svgElement || !this.svgElement.parentElement) return;

        const container = this.svgElement.parentElement;

        // Make sure the container has relative positioning
        const containerStyle = window.getComputedStyle(container);
        if (containerStyle.position === 'static') {
            container.style.position = 'relative';
        }

        // Create and add zoom controls
        const zoomControls = this.createZoomControls();
        container.appendChild(zoomControls);

        console.log('Zoom controls added to container');
    }

    /**
     * Create zoom control buttons
     * @returns {HTMLElement} - Zoom controls container
     */
    createZoomControls() {
        const controlsContainer = document.createElement('div');
        controlsContainer.className = 'zoom-controls';
        controlsContainer.style.cssText = `
            position: absolute;
            top: 10px;
            left: 10px;
            display: flex;
            flex-direction: column;
            gap: 5px;
            z-index: 1000;
            pointer-events: auto;
        `;

        // Zoom In button
        const zoomInBtn = document.createElement('button');
        zoomInBtn.innerHTML = '+';
        zoomInBtn.title = 'Zoom In';
        zoomInBtn.style.cssText = `
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            font-size: 20px;
            font-weight: bold;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        `;
        zoomInBtn.addEventListener('click', () => this.zoomIn());

        // Zoom Out button
        const zoomOutBtn = document.createElement('button');
        zoomOutBtn.innerHTML = '−';
        zoomOutBtn.title = 'Zoom Out';
        zoomOutBtn.style.cssText = `
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            font-size: 20px;
            font-weight: bold;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        `;
        zoomOutBtn.addEventListener('click', () => this.zoomOut());

        // Reset View button
        const resetBtn = document.createElement('button');
        resetBtn.innerHTML = '⌂';
        resetBtn.title = 'Reset View';
        resetBtn.style.cssText = `
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            font-size: 16px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        `;
        resetBtn.addEventListener('click', () => this.resetView());

        // Fit to View button
        const fitBtn = document.createElement('button');
        fitBtn.innerHTML = '⊞';
        fitBtn.title = 'Fit to View';
        fitBtn.style.cssText = `
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            font-size: 16px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        `;
        fitBtn.addEventListener('click', () => this.zoomToFit());

        // Add hover effects
        [zoomInBtn, zoomOutBtn, resetBtn, fitBtn].forEach(btn => {
            btn.addEventListener('mouseenter', () => {
                btn.style.background = 'rgba(0, 0, 0, 0.9)';
            });
            btn.addEventListener('mouseleave', () => {
                btn.style.background = 'rgba(0, 0, 0, 0.7)';
            });
        });

        controlsContainer.appendChild(zoomInBtn);
        controlsContainer.appendChild(zoomOutBtn);
        controlsContainer.appendChild(resetBtn);
        controlsContainer.appendChild(fitBtn);

        return controlsContainer;
    }

    /**
     * Zoom in by a fixed amount
     */
    zoomIn() {
        const zoomFactor = 0.8; // Zoom in by 20%
        const centerX = this.viewBox.x + this.viewBox.width / 2;
        const centerY = this.viewBox.y + this.viewBox.height / 2;

        const newWidth = this.viewBox.width * zoomFactor;
        const newHeight = this.viewBox.height * zoomFactor;

        // Check zoom limits
        const newZoomLevel = 1000 / newWidth;
        if (newZoomLevel > this.maxZoom) return;

        this.viewBox.x = centerX - newWidth / 2;
        this.viewBox.y = centerY - newHeight / 2;
        this.viewBox.width = newWidth;
        this.viewBox.height = newHeight;
        this.zoomLevel = newZoomLevel;

        this.updateViewBox();
        this.updateMinimap();
    }

    /**
     * Zoom out by a fixed amount
     */
    zoomOut() {
        const zoomFactor = 1.25; // Zoom out by 25%
        const centerX = this.viewBox.x + this.viewBox.width / 2;
        const centerY = this.viewBox.y + this.viewBox.height / 2;

        const newWidth = this.viewBox.width * zoomFactor;
        const newHeight = this.viewBox.height * zoomFactor;

        // Check zoom limits
        const newZoomLevel = 1000 / newWidth;
        if (newZoomLevel < this.minZoom) return;

        this.viewBox.x = centerX - newWidth / 2;
        this.viewBox.y = centerY - newHeight / 2;
        this.viewBox.width = newWidth;
        this.viewBox.height = newHeight;
        this.zoomLevel = newZoomLevel;

        this.updateViewBox();
        this.updateMinimap();
    }

    render() {
        if (!this.svgElement) return;

        // Clear existing content
        this.svgElement.innerHTML = '';

        // Create gradients
        this.createGradients();

        // Render connections
        this.renderConnections();

        // Render special connections
        this.renderWorkerConnections();
        this.renderEventConnections();

        // Render nodes
        this.renderNodes();

        // Render arrows
        this.renderArrows();

        // Create or update minimap
        this.createMinimap();
        this.renderMinimap();
    }

    createGradients() {
        const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');

        // Worker gradient
        const workerGradient = document.createElementNS(
            'http://www.w3.org/2000/svg',
            'linearGradient',
        );
        workerGradient.setAttribute('id', 'workerGradient');
        workerGradient.setAttribute('x1', '0%');
        workerGradient.setAttribute('y1', '0%');
        workerGradient.setAttribute('x2', '100%');
        workerGradient.setAttribute('y2', '0%');

        const workerStops = [
            { offset: '0%', color: '#3b82f6', opacity: '1' },
            { offset: '50%', color: '#3b82f6', opacity: '0.2' },
            { offset: '100%', color: '#3b82f6', opacity: '1' },
        ];

        workerStops.forEach((stop) => {
            const stopElement = document.createElementNS('http://www.w3.org/2000/svg', 'stop');
            stopElement.setAttribute('offset', stop.offset);
            stopElement.setAttribute('stop-color', stop.color);
            stopElement.setAttribute('stop-opacity', stop.opacity);
            workerGradient.appendChild(stopElement);
        });

        // Event gradient
        const eventGradient = document.createElementNS(
            'http://www.w3.org/2000/svg',
            'linearGradient',
        );
        eventGradient.setAttribute('id', 'eventGradient');
        eventGradient.setAttribute('x1', '0%');
        eventGradient.setAttribute('y1', '0%');
        eventGradient.setAttribute('x2', '100%');
        eventGradient.setAttribute('y2', '0%');

        const eventStops = [
            { offset: '0%', color: '#eab308', opacity: '1' },
            { offset: '50%', color: '#eab308', opacity: '0.2' },
            { offset: '100%', color: '#eab308', opacity: '1' },
        ];

        eventStops.forEach((stop) => {
            const stopElement = document.createElementNS('http://www.w3.org/2000/svg', 'stop');
            stopElement.setAttribute('offset', stop.offset);
            stopElement.setAttribute('stop-color', stop.color);
            stopElement.setAttribute('stop-opacity', stop.opacity);
            eventGradient.appendChild(stopElement);
        });

        defs.appendChild(workerGradient);
        defs.appendChild(eventGradient);
        this.svgElement.appendChild(defs);
    }

    renderConnections() {
        this.connections.forEach((conn) => {
            const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            path.setAttribute('d', this.getConnectionPath(conn.from, conn.to));
            path.setAttribute('stroke', '#6b7280');
            path.setAttribute('stroke-width', '1.5');
            path.setAttribute('fill', 'none');
            path.setAttribute('stroke-dasharray', '3,3');
            path.setAttribute('opacity', '0.7');
            this.svgElement.appendChild(path);
        });
    }

    renderWorkerConnections() {
        this.workerConnections.forEach((conn) => {
            const { path, endX, endY } = this.getSpecialConnectionPath(
                conn.from,
                conn.to,
                'worker',
            );

            const g = document.createElementNS('http://www.w3.org/2000/svg', 'g');

            const pathElement = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            pathElement.setAttribute('d', path);
            pathElement.setAttribute('stroke', 'url(#workerGradient)');
            pathElement.setAttribute('stroke-width', '2');
            pathElement.setAttribute('fill', 'none');
            pathElement.setAttribute('stroke-dasharray', '4,4');
            pathElement.setAttribute('opacity', '0.8');

            const arrow = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
            arrow.setAttribute(
                'points',
                `${endX - 6},${endY - 3} ${endX + 2},${endY} ${endX - 6},${endY + 3}`,
            );
            arrow.setAttribute('fill', '#3b82f6');
            arrow.setAttribute('opacity', '0.8');

            g.appendChild(pathElement);
            g.appendChild(arrow);
            this.svgElement.appendChild(g);
        });
    }

    renderEventConnections() {
        this.eventConnections.forEach((conn) => {
            const { path, endX, endY } = this.getSpecialConnectionPath(conn.from, conn.to, 'event');

            const g = document.createElementNS('http://www.w3.org/2000/svg', 'g');

            const pathElement = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            pathElement.setAttribute('d', path);
            pathElement.setAttribute('stroke', 'url(#eventGradient)');
            pathElement.setAttribute('stroke-width', '2');
            pathElement.setAttribute('fill', 'none');
            pathElement.setAttribute('stroke-dasharray', '4,4');
            pathElement.setAttribute('opacity', '0.8');

            const arrow = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
            arrow.setAttribute(
                'points',
                `${endX - 6},${endY - 3} ${endX + 2},${endY} ${endX - 6},${endY + 3}`,
            );
            arrow.setAttribute('fill', '#eab308');
            arrow.setAttribute('opacity', '0.8');

            g.appendChild(pathElement);
            g.appendChild(arrow);
            this.svgElement.appendChild(g);
        });
    }

    renderNodes() {
        this.nodes.forEach((node) => {
            const g = document.createElementNS('http://www.w3.org/2000/svg', 'g');
            g.setAttribute('class', 'node');

            // Node background
            const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
            rect.setAttribute('x', node.x);
            rect.setAttribute('y', node.y);
            rect.setAttribute('width', node.width);
            rect.setAttribute('height', node.height);
            rect.setAttribute('rx', '6');
            rect.setAttribute('fill', '#374151');
            rect.setAttribute('stroke', '#6b7280');
            rect.setAttribute('stroke-width', '1');
            rect.style.cursor = 'move';
            rect.addEventListener('mousedown', (e) => this.handleMouseDown(e, node.id));

            // Node text
            const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
            text.setAttribute('x', node.x + node.width / 2);
            text.setAttribute('y', node.y + node.height / 2 - 6);
            text.setAttribute('text-anchor', 'middle');
            text.setAttribute('dominant-baseline', 'middle');
            text.setAttribute('fill', '#e5e7eb');
            text.setAttribute('font-size', '10');
            text.setAttribute('font-weight', '500');
            text.style.pointerEvents = 'none';
            text.style.userSelect = 'none';
            text.textContent = node.text;

            // Filename text
            const filename = document.createElementNS('http://www.w3.org/2000/svg', 'text');
            filename.setAttribute('x', node.x + node.width / 2);
            filename.setAttribute('y', node.y + node.height / 2 + 6);
            filename.setAttribute('text-anchor', 'middle');
            filename.setAttribute('dominant-baseline', 'middle');
            filename.setAttribute('fill', '#9ca3af');
            filename.setAttribute('font-size', '8');
            filename.setAttribute('font-weight', '400');
            filename.style.pointerEvents = 'none';
            filename.style.userSelect = 'none';
            filename.textContent = node.filename;

            g.appendChild(rect);
            g.appendChild(text);
            g.appendChild(filename);

            // Add indicators
            if (node.hasImport) {
                const importCircle = document.createElementNS(
                    'http://www.w3.org/2000/svg',
                    'circle',
                );
                importCircle.setAttribute('cx', node.x - 8);
                importCircle.setAttribute('cy', node.y + node.height / 2);
                importCircle.setAttribute('r', '4');
                importCircle.setAttribute('fill', '#ef4444');
                importCircle.setAttribute('stroke', '#dc2626');
                importCircle.setAttribute('stroke-width', '1');
                g.appendChild(importCircle);
            }

            if (node.hasExport) {
                const exportCircle = document.createElementNS(
                    'http://www.w3.org/2000/svg',
                    'circle',
                );
                exportCircle.setAttribute('cx', node.x + node.width + 8);
                exportCircle.setAttribute('cy', node.y + node.height / 2);
                exportCircle.setAttribute('r', '4');
                exportCircle.setAttribute('fill', '#22c55e');
                exportCircle.setAttribute('stroke', '#16a34a');
                exportCircle.setAttribute('stroke-width', '1');
                g.appendChild(exportCircle);
            }

            if (node.hasEvent) {
                const eventCircle = document.createElementNS(
                    'http://www.w3.org/2000/svg',
                    'circle',
                );
                eventCircle.setAttribute('cx', node.x + node.width / 2);
                eventCircle.setAttribute('cy', node.y + node.height + 8);
                eventCircle.setAttribute('r', '4');
                eventCircle.setAttribute('fill', '#eab308');
                eventCircle.setAttribute('stroke', '#ca8a04');
                eventCircle.setAttribute('stroke-width', '1');
                g.appendChild(eventCircle);
            }

            if (node.hasWorker) {
                const workerCircle = document.createElementNS(
                    'http://www.w3.org/2000/svg',
                    'circle',
                );
                workerCircle.setAttribute('cx', node.x + node.width / 2);
                workerCircle.setAttribute('cy', node.y - 8);
                workerCircle.setAttribute('r', '4');
                workerCircle.setAttribute('fill', '#3b82f6');
                workerCircle.setAttribute('stroke', '#2563eb');
                workerCircle.setAttribute('stroke-width', '1');
                g.appendChild(workerCircle);
            }

            this.svgElement.appendChild(g);
        });
    }

    renderArrows() {
        this.connections.forEach((conn) => {
            const fromNode = this.nodes.find((n) => n.id === conn.from);
            const toNode = this.nodes.find((n) => n.id === conn.to);
            const toX = toNode.x;
            const toY = toNode.y + toNode.height / 2;

            const arrow = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
            arrow.setAttribute(
                'points',
                `${toX - 6},${toY - 3} ${toX},${toY} ${toX - 6},${toY + 3}`,
            );
            arrow.setAttribute('fill', '#6b7280');
            arrow.setAttribute('opacity', '0.7');
            this.svgElement.appendChild(arrow);
        });
    }

    /**
     * Create minimap container and SVG
     */
    createMinimap() {
        if (this.minimapContainer) return; // Already created

        // Find the parent container of the main SVG
        const parentContainer = this.svgElement.parentElement;
        if (!parentContainer) return;

        // Create minimap container
        this.minimapContainer = document.createElement('div');
        this.minimapContainer.style.cssText = `
            position: absolute;
            top: 10px;
            right: 10px;
            width: ${this.minimapWidth}px;
            height: ${this.minimapHeight}px;
            background: rgba(31, 41, 55, 0.9);
            border: 1px solid #6b7280;
            border-radius: 4px;
            overflow: hidden;
            z-index: 1000;
            cursor: pointer;
        `;

        // Create minimap SVG
        this.minimapSvg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
        this.minimapSvg.setAttribute('width', '100%');
        this.minimapSvg.setAttribute('height', '100%');
        this.minimapSvg.setAttribute('viewBox', `0 0 1000 500`);
        this.minimapSvg.style.cssText = `
            width: 100%;
            height: 100%;
        `;

        // Add event listeners for minimap interaction
        this.minimapSvg.addEventListener('mousedown', this.handleMinimapMouseDown);

        this.minimapContainer.appendChild(this.minimapSvg);
        parentContainer.appendChild(this.minimapContainer);
    }

    /**
     * Render minimap content
     */
    renderMinimap() {
        if (!this.minimapSvg) return;

        // Clear minimap content
        this.minimapSvg.innerHTML = '';

        // Render minimap nodes (simplified)
        this.nodes.forEach((node) => {
            const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
            rect.setAttribute('x', node.x);
            rect.setAttribute('y', node.y);
            rect.setAttribute('width', node.width);
            rect.setAttribute('height', node.height);
            rect.setAttribute('rx', '2');
            rect.setAttribute('fill', '#374151');
            rect.setAttribute('stroke', '#6b7280');
            rect.setAttribute('stroke-width', '0.5');
            this.minimapSvg.appendChild(rect);
        });

        // Render minimap connections (simplified)
        this.connections.forEach((conn) => {
            const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            path.setAttribute('d', this.getConnectionPath(conn.from, conn.to));
            path.setAttribute('stroke', '#6b7280');
            path.setAttribute('stroke-width', '0.5');
            path.setAttribute('fill', 'none');
            path.setAttribute('opacity', '0.5');
            this.minimapSvg.appendChild(path);
        });

        // Create viewport indicator
        this.createViewportIndicator();
    }

    /**
     * Create viewport indicator on minimap
     */
    createViewportIndicator() {
        if (!this.minimapSvg) return;

        // Remove existing viewport indicator
        const existingViewport = this.minimapSvg.querySelector('.viewport-indicator');
        if (existingViewport) {
            existingViewport.remove();
        }

        // Calculate viewport bounds based on current view
        const viewportX = this.viewBox.x;
        const viewportY = this.viewBox.y;
        const viewportWidth = this.viewBox.width;
        const viewportHeight = this.viewBox.height;

        this.viewportRect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
        this.viewportRect.setAttribute('class', 'viewport-indicator');
        this.viewportRect.setAttribute('x', viewportX);
        this.viewportRect.setAttribute('y', viewportY);
        this.viewportRect.setAttribute('width', viewportWidth);
        this.viewportRect.setAttribute('height', viewportHeight);
        this.viewportRect.setAttribute('fill', 'none');
        this.viewportRect.setAttribute('stroke', '#3b82f6');
        this.viewportRect.setAttribute('stroke-width', '2');
        this.viewportRect.setAttribute('opacity', '0.8');
        this.viewportRect.style.pointerEvents = 'none';

        this.minimapSvg.appendChild(this.viewportRect);
    }

    /**
     * Handle minimap mouse down event
     */
    handleMinimapMouseDown(e) {
        e.preventDefault();
        this.isMinimapDragging = true;
        
        // Add event listeners for minimap dragging
        document.addEventListener('mousemove', this.handleMinimapMouseMove);
        document.addEventListener('mouseup', this.handleMinimapMouseUp);
        
        // Handle initial click to center view
        this.handleMinimapClick(e);
    }

    /**
     * Handle minimap mouse move event
     */
    handleMinimapMouseMove(e) {
        if (!this.isMinimapDragging) return;
        e.preventDefault();
        this.handleMinimapClick(e);
    }

    /**
     * Handle minimap mouse up event
     */
    handleMinimapMouseUp(e) {
        this.isMinimapDragging = false;
        document.removeEventListener('mousemove', this.handleMinimapMouseMove);
        document.removeEventListener('mouseup', this.handleMinimapMouseUp);
    }

    /**
     * Handle minimap click to center view
     */
    handleMinimapClick(e) {
        if (!this.minimapSvg || !this.viewportRect) return;

        const minimapRect = this.minimapSvg.getBoundingClientRect();
        const clickX = ((e.clientX - minimapRect.left) / minimapRect.width) * 1000;
        const clickY = ((e.clientY - minimapRect.top) / minimapRect.height) * 500;

        // Update the main view to center on the clicked position
        this.viewBox.x = clickX - this.viewBox.width / 2;
        this.viewBox.y = clickY - this.viewBox.height / 2;

        // Apply the new viewBox
        this.updateViewBox();
        this.updateMinimap();
    }

    /**
     * Update minimap to reflect current view
     */
    updateMinimap() {
        if (!this.viewportRect) return;

        // Update viewport indicator position and size
        this.viewportRect.setAttribute('x', this.viewBox.x);
        this.viewportRect.setAttribute('y', this.viewBox.y);
        this.viewportRect.setAttribute('width', this.viewBox.width);
        this.viewportRect.setAttribute('height', this.viewBox.height);
    }

    /**
     * Toggle minimap visibility
     */
    toggleMinimap() {
        if (!this.minimapContainer) return;

        const isVisible = this.minimapContainer.style.display !== 'none';
        this.minimapContainer.style.display = isVisible ? 'none' : 'block';
    }

    /**
     * Destroy minimap
     */
    destroyMinimap() {
        if (this.minimapContainer) {
            this.minimapContainer.remove();
            this.minimapContainer = null;
            this.minimapSvg = null;
            this.viewportRect = null;
        }
    }

    getNodes() {
        return this.nodes;
    }

    getConnections() {
        return this.connections;
    }

    getWorkerConnections() {
        return this.workerConnections;
    }

    getEventConnections() {
        return this.eventConnections;
    }

    updateNodePosition(nodeId, x, y) {
        this.nodes = this.nodes.map((node) => (node.id === nodeId ? { ...node, x, y } : node));
        this.render();
    }

    addNode(nodeData) {
        this.nodes.push(nodeData);
        this.render();
    }

    removeNode(nodeId) {
        this.nodes = this.nodes.filter((node) => node.id !== nodeId);
        this.connections = this.connections.filter(
            (conn) => conn.from !== nodeId && conn.to !== nodeId,
        );
        this.workerConnections = this.workerConnections.filter(
            (conn) => conn.from !== nodeId && conn.to !== nodeId,
        );
        this.eventConnections = this.eventConnections.filter(
            (conn) => conn.from !== nodeId && conn.to !== nodeId,
        );
        this.render();
    }

    addConnection(from, to, type = 'normal') {
        const connection = { from, to };

        if (type === 'worker') {
            connection.type = 'worker';
            this.workerConnections.push(connection);
        } else if (type === 'event') {
            connection.type = 'event';
            this.eventConnections.push(connection);
        } else {
            this.connections.push(connection);
        }
        this.render();
    }

    exportData() {
        return {
            nodes: this.nodes,
            connections: this.connections,
            workerConnections: this.workerConnections,
            eventConnections: this.eventConnections,
        };
    }

    importData(data) {
        this.nodes = data.nodes || [];
        this.connections = data.connections || [];
        this.workerConnections = data.workerConnections || [];
        this.eventConnections = data.eventConnections || [];
        this.render();
    }

    /**
     * Update mindmap with new external data
     * @param {Object} externalData - New external data
     */
    updateData(externalData) {
        this.externalData = externalData;
        this.initializeMindmap(externalData);
        this.render();
    }

    /**
     * Set data model (for compatibility with dependency-mindmap.js)
     * @param {Object} dataModel - Data model with getNodes() and getLinks() methods
     */
    setDataModel(dataModel) {
        if (dataModel && typeof dataModel.getNodes === 'function' && typeof dataModel.getLinks === 'function') {
            const externalData = {
                nodes: dataModel.getNodes(),
                links: dataModel.getLinks()
            };
            this.updateData(externalData);
        }
    }

    destroy() {
        this.removeEventListeners();
        this.destroyMinimap();
        this.svgElement = null;
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MindmapProject;
} else if (typeof window !== 'undefined') {
    window.MindmapProject = MindmapProject;
}
