/**
 * Resilient Worker Pool
 * Provides robust worker management with health monitoring and automatic recovery
 */

class ResilientWorkerPool {
    constructor(options = {}) {
        this.workerScriptPath = options.workerScriptPath || 'js/core/scanner.worker.js';
        this.maxWorkers = options.maxWorkers || Math.min(navigator.hardwareConcurrency || 4, 4);
        this.workerTimeout = options.workerTimeout || 30000; // 30 seconds
        this.healthCheckInterval = options.healthCheckInterval || 5000; // 5 seconds
        this.maxRetries = options.maxRetries || 3;
        this.messageTimeout = options.messageTimeout || 10000; // 10 seconds
        
        // Worker pool state
        this.workers = new Map();
        this.availableWorkers = [];
        this.busyWorkers = new Set();
        this.workerHealth = new Map();
        this.messageQueue = [];
        this.activeMessages = new Map();
        
        // Statistics
        this.stats = {
            workersCreated: 0,
            workersTerminated: 0,
            messagesProcessed: 0,
            messagesFailed: 0,
            retriesPerformed: 0,
            healthChecksPerformed: 0
        };
        
        // Health monitoring
        this.healthMonitor = null;
        this.isShuttingDown = false;
        
        // Bind methods
        this.createWorker = this.createWorker.bind(this);
        this.terminateWorker = this.terminateWorker.bind(this);
        this.processWithRetry = this.processWithRetry.bind(this);
        this.startHealthMonitoring = this.startHealthMonitoring.bind(this);
        this.stopHealthMonitoring = this.stopHealthMonitoring.bind(this);
    }

    /**
     * Initialize the worker pool
     * @returns {Promise<void>}
     */
    async initialize() {
        console.log(`Initializing worker pool with ${this.maxWorkers} workers`);
        
        // Create initial workers
        const workerPromises = [];
        for (let i = 0; i < this.maxWorkers; i++) {
            workerPromises.push(this.createWorker());
        }
        
        try {
            await Promise.all(workerPromises);
            this.startHealthMonitoring();
            console.log(`Worker pool initialized successfully with ${this.availableWorkers.length} workers`);
        } catch (error) {
            console.error('Failed to initialize worker pool:', error);
            throw new Error(`Worker pool initialization failed: ${error.message}`);
        }
    }

    /**
     * Create a new worker with proper error handling
     * @returns {Promise<string>} - Worker ID
     */
    async createWorker() {
        const workerId = `worker_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        try {
            const worker = new Worker(this.workerScriptPath);
            
            if (!worker) {
                throw new Error('Worker creation returned null/undefined');
            }
            
            // Set up worker properties
            worker.workerId = workerId;
            worker.createdAt = Date.now();
            worker.lastActivity = Date.now();
            worker.messageCount = 0;
            worker.errorCount = 0;
            worker.isTerminated = false;
            
            // Set up message handling
            worker.onmessage = (event) => this.handleWorkerMessage(workerId, event);
            worker.onerror = (error) => this.handleWorkerError(workerId, error);
            worker.onmessageerror = (error) => this.handleWorkerMessageError(workerId, error);
            
            // Override terminate to track state
            const originalTerminate = worker.terminate.bind(worker);
            worker.terminate = () => {
                worker.isTerminated = true;
                return originalTerminate();
            };
            
            // Store worker
            this.workers.set(workerId, worker);
            this.availableWorkers.push(workerId);
            this.workerHealth.set(workerId, {
                status: 'healthy',
                lastHealthCheck: Date.now(),
                consecutiveFailures: 0,
                totalMessages: 0,
                totalErrors: 0
            });
            
            this.stats.workersCreated++;
            console.log(`Created worker ${workerId}`);
            
            return workerId;
            
        } catch (error) {
            console.error(`Failed to create worker ${workerId}:`, error);
            throw new Error(`Worker creation failed: ${error.message}`);
        }
    }

    /**
     * Handle worker message with timeout and error handling
     * @param {string} workerId - Worker ID
     * @param {MessageEvent} event - Message event
     */
    handleWorkerMessage(workerId, event) {
        try {
            const worker = this.workers.get(workerId);
            if (!worker) return;
            
            worker.lastActivity = Date.now();
            worker.messageCount++;
            
            const data = event.data;
            if (!data || typeof data !== 'object') {
                console.error(`Worker ${workerId} sent invalid data:`, data);
                return;
            }
            
            // Update health status
            const health = this.workerHealth.get(workerId);
            if (health) {
                health.totalMessages++;
                health.consecutiveFailures = 0;
                health.status = 'healthy';
            }
            
            // Handle message based on type
            if (data.messageId && this.activeMessages.has(data.messageId)) {
                const messageHandler = this.activeMessages.get(data.messageId);
                this.activeMessages.delete(data.messageId);
                
                if (messageHandler.timeout) {
                    clearTimeout(messageHandler.timeout);
                }
                
                // Mark worker as available again
                this.markWorkerAvailable(workerId);
                
                messageHandler.resolve(data);
                this.stats.messagesProcessed++;
                
            } else {
                // Handle broadcast messages (no specific handler)
                this.dispatchEvent('worker:message', {
                    workerId,
                    data,
                    timestamp: Date.now()
                });
                
                // Handle heartbeat messages to keep worker alive
                if (data.type === 'heartbeat') {
                    // Update worker activity time to prevent stale detection
                    worker.lastActivity = Date.now();
                    console.log(`Heartbeat received from worker ${workerId}`);
                }
                
                // For certain message types, mark worker as available
                if (data.type === 'processing_progress' || data.type === 'debug' || data.type === 'workerResults') {
                    // Don't mark as available for progress messages, only for completion
                    if (data.type === 'workerResults') {
                        this.markWorkerAvailable(workerId);
                    }
                }
            }
            
        } catch (error) {
            console.error(`Error handling worker message from ${workerId}:`, error);
            this.handleWorkerError(workerId, error);
        }
    }

    /**
     * Handle worker error
     * @param {string} workerId - Worker ID
     * @param {Error|ErrorEvent} error - Error object
     */
    handleWorkerError(workerId, error) {
        console.error(`Worker ${workerId} error:`, error);
        
        const worker = this.workers.get(workerId);
        if (worker) {
            worker.errorCount++;
        }
        
        // Update health status
        const health = this.workerHealth.get(workerId);
        if (health) {
            health.totalErrors++;
            health.consecutiveFailures++;
            health.status = health.consecutiveFailures > 3 ? 'unhealthy' : 'degraded';
        }
        
        // Dispatch error event
        this.dispatchEvent('worker:error', {
            workerId,
            error: {
                message: error.message || 'Unknown worker error',
                filename: error.filename,
                lineno: error.lineno,
                colno: error.colno
            },
            timestamp: Date.now()
        });
        
        // Consider replacing unhealthy worker
        if (health && health.status === 'unhealthy') {
            this.replaceWorker(workerId);
        }
    }

    /**
     * Handle worker message error
     * @param {string} workerId - Worker ID
     * @param {MessageEvent} error - Message error event
     */
    handleWorkerMessageError(workerId, error) {
        console.error(`Worker ${workerId} message error:`, error);
        this.handleWorkerError(workerId, new Error(`Message error: ${error.data || 'Unknown'}`));
    }

    /**
     * Send message to worker with retry logic
     * @param {Object} message - Message to send
     * @param {Object} options - Processing options
     * @returns {Promise<Object>} - Worker response
     */
    async processWithRetry(message, options = {}) {
        const maxRetries = options.maxRetries || this.maxRetries;
        const timeout = options.timeout || this.messageTimeout;
        
        let lastError = null;
        
        for (let attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                if (attempt > 0) {
                    this.stats.retriesPerformed++;
                    console.log(`Retry attempt ${attempt} for message`);
                    
                    // Wait before retry with exponential backoff
                    const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
                    await this.delay(delay);
                }
                
                // For processAll messages, we need to wait for the actual results
                if (message.type === 'processAll') {
                    const result = await this.sendMessageAndWaitForResults(message, timeout);
                    return result;
                } else {
                    const result = await this.sendMessage(message, timeout);
                    return result;
                }
                
            } catch (error) {
                lastError = error;
                console.warn(`Attempt ${attempt + 1} failed:`, error.message);
                
                // If it's a worker-specific error, try a different worker
                if (error.workerId) {
                    await this.handleWorkerFailure(error.workerId);
                }
            }
        }
        
        this.stats.messagesFailed++;
        throw new Error(`Failed after ${maxRetries + 1} attempts. Last error: ${lastError?.message}`);
    }

    /**
     * Send message to an available worker
     * @param {Object} message - Message to send
     * @param {number} timeout - Message timeout
     * @returns {Promise<Object>} - Worker response
     */
    async sendMessage(message, timeout = this.messageTimeout) {
        const workerId = await this.getAvailableWorker();
        const worker = this.workers.get(workerId);

        if (!worker || worker.isTerminated) {
            throw new Error(`Worker ${workerId} is not available`);
        }

        const messageId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const messageWithId = { ...message, messageId };

        console.log(`ResilientWorkerPool: Sending ${message.type} message to worker ${workerId}`);
        
        return new Promise((resolve, reject) => {
            const timeoutHandle = setTimeout(() => {
                this.activeMessages.delete(messageId);
                reject(new Error(`Message timeout after ${timeout}ms`));
            }, timeout);
            
            this.activeMessages.set(messageId, {
                resolve,
                reject,
                timeout: timeoutHandle,
                workerId,
                timestamp: Date.now()
            });
            
            try {
                worker.postMessage(messageWithId);
                this.markWorkerBusy(workerId);
                
            } catch (error) {
                this.activeMessages.delete(messageId);
                clearTimeout(timeoutHandle);
                reject(new Error(`Failed to send message: ${error.message}`));
            }
        });
    }

    /**
     * Send processAll message and wait for actual results
     * @param {Object} message - Message to send
     * @param {number} timeout - Message timeout
     * @returns {Promise<Object>} - Worker response with actual data
     */
    async sendMessageAndWaitForResults(message, timeout = this.messageTimeout) {
        const workerId = await this.getAvailableWorker();
        const worker = this.workers.get(workerId);

        if (!worker || worker.isTerminated) {
            throw new Error(`Worker ${workerId} is not available`);
        }

        const messageId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const messageWithId = { ...message, messageId };

        console.log(`ResilientWorkerPool: Sending ${message.type} message to worker ${workerId} (with results wait)`);
        
        return new Promise((resolve, reject) => {
            let ackReceived = false;
            let resultsReceived = false;
            
            const timeoutHandle = setTimeout(() => {
                this.activeMessages.delete(messageId);
                this.removeEventListener('worker:message', resultListener);
                reject(new Error(`Message timeout after ${timeout}ms`));
            }, timeout);
            
            // Listen for acknowledgment
            this.activeMessages.set(messageId, {
                resolve: (ackData) => {
                    ackReceived = true;
                    console.log('ProcessAll acknowledgment received:', ackData.message);
                    // Don't resolve yet, wait for actual results
                },
                reject,
                timeout: timeoutHandle,
                workerId,
                timestamp: Date.now()
            });
            
            // Listen for actual results
            const resultListener = (event) => {
                const { data } = event.detail;
                if (data.type === 'workerResults' && !resultsReceived) {
                    resultsReceived = true;
                    this.activeMessages.delete(messageId);
                    this.removeEventListener('worker:message', resultListener);
                    clearTimeout(timeoutHandle);
                    
                    // Mark worker as available
                    this.markWorkerAvailable(workerId);
                    
                    resolve({
                        success: true,
                        data: data.data,
                        messageId: messageId
                    });
                }
            };
            
            this.addEventListener('worker:message', resultListener);
            
            try {
                worker.postMessage(messageWithId);
                this.markWorkerBusy(workerId);
                
            } catch (error) {
                this.activeMessages.delete(messageId);
                this.removeEventListener('worker:message', resultListener);
                clearTimeout(timeoutHandle);
                reject(new Error(`Failed to send message: ${error.message}`));
            }
        });
    }

    /**
     * Get an available worker
     * @returns {Promise<string>} - Worker ID
     */
    async getAvailableWorker() {
        // Wait for available worker with timeout
        const maxWait = 30000; // 30 seconds
        const startTime = Date.now();
        
        while (this.availableWorkers.length === 0) {
            if (Date.now() - startTime > maxWait) {
                throw new Error('No workers available within timeout period');
            }
            
            // Try to create a new worker if under limit
            if (this.workers.size < this.maxWorkers) {
                try {
                    return await this.createWorker();
                } catch (error) {
                    console.warn('Failed to create additional worker:', error);
                }
            }
            
            // Wait and check again
            await this.delay(100);
        }
        
        return this.availableWorkers.shift();
    }

    /**
     * Mark worker as busy
     * @param {string} workerId - Worker ID
     */
    markWorkerBusy(workerId) {
        const index = this.availableWorkers.indexOf(workerId);
        if (index > -1) {
            this.availableWorkers.splice(index, 1);
        }
        this.busyWorkers.add(workerId);
    }

    /**
     * Mark worker as available
     * @param {string} workerId - Worker ID
     */
    markWorkerAvailable(workerId) {
        this.busyWorkers.delete(workerId);
        if (!this.availableWorkers.includes(workerId)) {
            this.availableWorkers.push(workerId);
        }
    }

    /**
     * Replace an unhealthy worker
     * @param {string} workerId - Worker ID to replace
     */
    async replaceWorker(workerId) {
        console.log(`Replacing unhealthy worker ${workerId}`);
        
        try {
            // Terminate old worker
            await this.terminateWorker(workerId);
            
            // Create new worker
            await this.createWorker();
            
        } catch (error) {
            console.error(`Failed to replace worker ${workerId}:`, error);
        }
    }

    /**
     * Handle worker failure
     * @param {string} workerId - Failed worker ID
     */
    async handleWorkerFailure(workerId) {
        const health = this.workerHealth.get(workerId);
        if (health) {
            health.consecutiveFailures++;
            health.status = 'degraded';
            
            if (health.consecutiveFailures > 2) {
                await this.replaceWorker(workerId);
            }
        }
    }

    /**
     * Start health monitoring
     */
    startHealthMonitoring() {
        if (this.healthMonitor) return;
        
        this.healthMonitor = setInterval(() => {
            this.performHealthCheck();
        }, this.healthCheckInterval);
        
        console.log('Health monitoring started');
    }

    /**
     * Stop health monitoring
     */
    stopHealthMonitoring() {
        if (this.healthMonitor) {
            clearInterval(this.healthMonitor);
            this.healthMonitor = null;
            console.log('Health monitoring stopped');
        }
    }

    /**
     * Perform health check on all workers
     */
    async performHealthCheck() {
        if (this.isShuttingDown) return;
        
        this.stats.healthChecksPerformed++;
        const now = Date.now();
        
        for (const [workerId, worker] of this.workers) {
            const health = this.workerHealth.get(workerId);
            if (!health) continue;
            
            // Check if worker is responsive
            const timeSinceLastActivity = now - worker.lastActivity;
            const isStale = timeSinceLastActivity > this.workerTimeout;
            
            // Only consider worker stale if it's not busy and hasn't been active for a long time
            if (isStale && !this.busyWorkers.has(workerId)) {
                // Be more lenient with workers that might be processing large files
                const extendedTimeout = this.workerTimeout * 2; // Double the timeout for busy workers
                const isReallyStale = timeSinceLastActivity > extendedTimeout;
                
                if (isReallyStale) {
                    console.warn(`Worker ${workerId} appears stale, last activity: ${timeSinceLastActivity}ms ago`);
                    health.consecutiveFailures++;
                    health.status = 'degraded';
                    
                    if (health.consecutiveFailures > 5) { // Increased threshold
                        await this.replaceWorker(workerId);
                    }
                } else {
                    // Worker is slow but not stale yet
                    console.log(`Worker ${workerId} is slow but still processing, last activity: ${timeSinceLastActivity}ms ago`);
                }
            } else {
                health.lastHealthCheck = now;
                if (health.consecutiveFailures > 0) {
                    health.consecutiveFailures = Math.max(0, health.consecutiveFailures - 1); // Gradually recover
                }
            }
        }
    }

    /**
     * Terminate a specific worker
     * @param {string} workerId - Worker ID
     */
    async terminateWorker(workerId) {
        const worker = this.workers.get(workerId);
        if (!worker) return;
        
        try {
            // Send cleanup message first
            if (!worker.isTerminated) {
                worker.postMessage({ type: 'cleanup' });
                await this.delay(100); // Give time for cleanup
            }
            
            // Terminate worker
            worker.terminate();
            
        } catch (error) {
            console.warn(`Error during worker ${workerId} termination:`, error);
        } finally {
            // Clean up references
            this.workers.delete(workerId);
            this.workerHealth.delete(workerId);
            this.busyWorkers.delete(workerId);
            
            const index = this.availableWorkers.indexOf(workerId);
            if (index > -1) {
                this.availableWorkers.splice(index, 1);
            }
            
            this.stats.workersTerminated++;
            console.log(`Terminated worker ${workerId}`);
        }
    }

    /**
     * Shutdown the worker pool
     */
    async shutdown() {
        console.log('Shutting down worker pool...');
        this.isShuttingDown = true;
        
        // Stop health monitoring
        this.stopHealthMonitoring();
        
        // Cancel active messages
        for (const [messageId, handler] of this.activeMessages) {
            if (handler.timeout) {
                clearTimeout(handler.timeout);
            }
            handler.reject(new Error('Worker pool shutting down'));
        }
        this.activeMessages.clear();
        
        // Terminate all workers
        const terminationPromises = Array.from(this.workers.keys()).map(workerId => 
            this.terminateWorker(workerId)
        );
        
        await Promise.all(terminationPromises);
        
        // Force garbage collection
        if (window.gc) {
            setTimeout(() => window.gc(), 200);
        }
        
        console.log('Worker pool shutdown complete');
    }

    /**
     * Get pool statistics
     * @returns {Object} - Pool statistics
     */
    getStats() {
        return {
            ...this.stats,
            activeWorkers: this.workers.size,
            availableWorkers: this.availableWorkers.length,
            busyWorkers: this.busyWorkers.size,
            activeMessages: this.activeMessages.size,
            workerHealth: Array.from(this.workerHealth.entries()).map(([id, health]) => ({
                workerId: id,
                ...health
            }))
        };
    }

    /**
     * Dispatch custom event
     * @param {string} eventName - Event name
     * @param {Object} detail - Event detail
     */
    dispatchEvent(eventName, detail) {
        const event = new CustomEvent(eventName, { detail });
        document.dispatchEvent(event);
    }

    /**
     * Add event listener
     * @param {string} eventName - Event name
     * @param {Function} listener - Event listener function
     */
    addEventListener(eventName, listener) {
        document.addEventListener(eventName, listener);
    }

    /**
     * Remove event listener
     * @param {string} eventName - Event name
     * @param {Function} listener - Event listener function
     */
    removeEventListener(eventName, listener) {
        document.removeEventListener(eventName, listener);
    }

    /**
     * Utility delay function
     * @param {number} ms - Milliseconds to delay
     */
    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

export default ResilientWorkerPool;